name: build

on:
  workflow_dispatch:
    inputs:
      admin_github_token:
        description: '管理员密钥'
        required: true
        type: string
      version_number:
        description: '镜像版本号'
        required: true
        type: string
        default: latest
      repository_account:
        description: '仓库'
        required: true
        type: string
        default: 'isxcode'
      branch_number:
        description: '分支'
        required: true
        type: string
        default: 'main'

env:
  ADMIN_GITHUB_TOKEN: ${{ inputs.admin_github_token }}

jobs:

  build:
    runs-on: ubuntu-latest

    permissions:
      contents: read
      packages: write
      id-token: write

    strategy:
      fail-fast: false
      matrix:
        node:
          - '18'

    steps:

      - name: Set timezone to Asia/Shanghai
        run: |
          sudo timedatectl set-timezone Asia/Shanghai
          date

      - name: Checkout torch-yun
        uses: actions/checkout@v4
        with:
          token: ${{ env.ADMIN_GITHUB_TOKEN }}
          repository: ${{ inputs.repository_account }}/torch-yun
          ref: ${{ inputs.branch_number }}

      - name: Checkout torch-yun-vip
        uses: actions/checkout@v4
        with:
          token: ${{ env.ADMIN_GITHUB_TOKEN }}
          repository: ${{ inputs.repository_account }}/torch-yun-vip
          ref: ${{ inputs.branch_number }}
          path: "/home/<USER>/work/torch-yun/torch-yun/torch-yun-vip"

      - name: Cache gradle
        uses: actions/cache@v3
        with:
          path: |
            ~/.gradle/caches
            ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
          restore-keys: |
            ${{ runner.os }}-gradle-

      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9.0.6
          run_install: false

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18

      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Setup jdk
        uses: actions/setup-java@v4
        with:
          distribution: 'zulu'
          java-version: '8'

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@417ae3ccd767c252f5661f1ace9f835f9654f2b5

      - name: Build with Gradle Wrapper
        run: ./gradlew install clean package

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ispong
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build and Push
        uses: docker/build-push-action@v5
        with:
          context: .
          platforms: linux/amd64,linux/arm64
          tags: isxcode/zhishuyun:${{ inputs.version_number }}
          push: true

      - name: Upload zhishuyun tar
        uses: actions/upload-artifact@v4
        with:
          name: zhishuyun
          path: ./torch-yun-dist/build/distributions/zhishuyun.tar.gz

  # 上传阿里oss使用单独任务，原因：上传失败后，可以使用github重跑任务，不需要重新打包
  upload-oss:
    needs: build

    runs-on: ubuntu-latest

    permissions:
      contents: read
      packages: write
      id-token: write

    steps:

      - name: Set timezone to Asia/Shanghai
        run: |
          sudo timedatectl set-timezone Asia/Shanghai
          date

      - name: Download zhishuyun tar
        uses: actions/download-artifact@v4
        with:
          name: zhishuyun
          path: ./

      - name: Set timezone to Asia/Shanghai
        run: |
          ls -lha

      - name: Upload Package to Ali oss
        uses: appleboy/scp-action@v1
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          password: ${{ secrets.PASSWORD }}
          port: 22
          timeout: 3600s
          source: "zhishuyun.tar.gz"
          target: /data/file/

  # 上传阿里镜像仓库使用单独任务，原因：上传失败后，可以使用github重跑任务，不需要重新打包
  upload-ali:
      needs: build

      runs-on: ubuntu-latest

      permissions:
        contents: read
        packages: write
        id-token: write

      steps:

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          registry: registry.cn-shanghai.aliyuncs.com
          username: ispong
          password: ${{ secrets.ALI_DOCKERHUB_TOKEN }}

      - name: Pull docker images
        run: |
          docker pull --platform=linux/amd64 isxcode/zhishuyun
          docker tag isxcode/zhishuyun registry.cn-shanghai.aliyuncs.com/isxcode/zhishuyun:latest-amd64
          docker push registry.cn-shanghai.aliyuncs.com/isxcode/zhishuyun:latest-amd64
          docker rmi isxcode/zhishuyun
          docker pull --platform=linux/arm64 isxcode/zhishuyun
          docker tag isxcode/zhishuyun registry.cn-shanghai.aliyuncs.com/isxcode/zhishuyun:latest-arm64
          docker push registry.cn-shanghai.aliyuncs.com/isxcode/zhishuyun:latest-arm64
