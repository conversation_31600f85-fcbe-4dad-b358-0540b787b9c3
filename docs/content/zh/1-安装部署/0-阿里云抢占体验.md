---
title: "阿里云抢占体验"
---

## 使用阿里云抢占服务器体验至数云平台

#### 1. 抢占阿里云服务器

阿里云链接:  https://ecs.console.aliyun.com/

> 选择`抢占式实例`，8核心16GB任意选择

![20250424170119](https://img.isxcode.com/picgo/20250424170119.png)

> 选择`Centos 8.5 64位`的系统版本

![20250424170158](https://img.isxcode.com/picgo/20250424170158.png)

> 注意！！安全组需要开放端口号: `8080`(访问端口必须开放，至数云默认端口号8080)

![20241101175947](https://img.isxcode.com/picgo/20241101175947.png)

> 获取服务器的`内网ip`和`外网ip`

![20250424170421](https://img.isxcode.com/picgo/20250424170421.png)

##### 系统信息如下

> 系统: Centos 8.5    
> 资源: 8核16GB  
> 外网ip: *************  
> 内网ip: *************   
> 账号: root  
> 密码: zhishuyun123..

#### 2. 进入服务器

```bash
ssh root@*************
```

![20241101180227](https://img.isxcode.com/picgo/20241101180227.png)

#### 3. 安装java环境(在线安装/推荐)

> Ubuntu系统，执行命令如下:  
> sudo apt update  
> sudo apt install openjdk-8-jdk openjdk-8-jre -y

```bash
sudo yum install java-1.8.0-openjdk-devel java-1.8.0-openjdk -y 
```

![20241101180421](https://img.isxcode.com/picgo/20241101180421.png)

#### 安装java环境(离线安装/可选)

```bash
cd /tmp
nohup wget https://openfly.oss-cn-shanghai.aliyuncs.com/java/zulu8.78.0.19-ca-jdk8.0.412-linux_x64.tar.gz >> download_jdk.log 2>&1 &
tail -f download_jdk.log
```

> 注意！！！一定要在~/.bashrc中配置JAVA_HOME环境变量

```bash
tar -vzxf /tmp/zulu8.78.0.19-ca-jdk8.0.412-linux_x64.tar.gz -C /opt
ln -s /opt/zulu8.78.0.19-ca-jdk8.0.412-linux_x64 /opt/java
tee -a ~/.bashrc <<-'EOF'
export JAVA_HOME=/opt/java
export PATH=$PATH:$JAVA_HOME/bin
EOF
source ~/.bashrc
java -version
```

#### 4. 升级Python版本(在线安装/推荐)

```bash
mv /etc/yum.repos.d/CentOS-Base.repo /etc/yum.repos.d/CentOS-Base.repo.backup
wget -O /etc/yum.repos.d/CentOS-Base.repo https://mirrors.aliyun.com/repo/Centos-vault-8.5.2111.repo
yum clean all && yum makecache
yum remove -y python3
yum install -y python39 
```

#### 5. 安装Pip依赖(在线安装/推荐)

> 安装huggingFace的transformers组件和torch环境

```bash
pip3 install torch 'transformers[torch]' fastapi uvicorn 
```

#### 6. 下载至数云安装包

> 等待时间比较久，大约1GB安装包

```bash
cd /tmp
nohup wget https://isxcode.oss-cn-shanghai.aliyuncs.com/zhishuyun/zhishuyun.tar.gz >> download_zhishuyun.log 2>&1 &
tail -f download_zhishuyun.log
```

![20241101181047](https://img.isxcode.com/picgo/20241101181047.png)

#### 7. 解压安装包

```bash
cd /tmp
tar -vzxf zhishuyun.tar.gz
```

![20250509153923](https://img.isxcode.com/picgo/20250509153923.png)

#### 8. 启动至数云

```bash
cd /tmp/zhishuyun/bin
bash start.sh
```

![20250509153956](https://img.isxcode.com/picgo/20250509153956.png)

#### 9. 检测服务是否启动

健康检测接口: http://*************:8080/tools/open/health

![20241101181317](https://img.isxcode.com/picgo/20241101181317.png)

#### 10. 访问至数云服务

> 1M的带宽，首次加载，大约40s

- 访问接口: http://*************:8080 
- 后台管理员账号: `admin` 
- 默认密码: `admin123`

![20250509154052](https://img.isxcode.com/picgo/20250509154052.png)

#### 11. 创建用户租户

- 创建用户`zhishuyun` 
- 创建租户`体验租户`

#### 12. 上传许可证

> 仅使用基础功能，不需要上传许可证  
> 可免费获取体验许可证，在官网的最下面  

官网链接: https://zhishuyun.isxcode.com/

![20250424170745](https://img.isxcode.com/picgo/20250424170745.png)

#### 13. 安装集群

> 退出后台管理界面，使用`zhishuyun`账号登录

![20250509154254](https://img.isxcode.com/picgo/20250509154254.png)

![20250509154353](https://img.isxcode.com/picgo/20250509154353.png)

>  选择`HuggingFace`类型的集群

![20250509154426](https://img.isxcode.com/picgo/20250509154426.png)

> 点击集群名称

![20250509155730](https://img.isxcode.com/picgo/20250509155730.png)

> 推荐使用内网ip  
> host: *************  
> 用户名:  root   
> 密码:  zhishuyun123..

![20250509160051](https://img.isxcode.com/picgo/20250509160051.png)

![20250509160202](https://img.isxcode.com/picgo/20250509160202.png)

![20250509160308](https://img.isxcode.com/picgo/20250509160308.png)

![20250509160353](https://img.isxcode.com/picgo/20250509160353.png)

#### 14. 下载离线大模型

> 使用`Qwen/Qwen2.5-0.5B-Instruct`为例  
> 将下载的内容压缩成`zip`包

- https://huggingface.co/Qwen/Qwen2.5-0.5B-Instruct/tree/main

![20250522181748](https://img.isxcode.com/picgo/20250522181748.png)

![20250509160901](https://img.isxcode.com/picgo/20250509160901.png)

#### 15. 上传离线大模型

![20250509170734](https://img.isxcode.com/picgo/20250509170734.png)

#### 16. 创建模型仓库

![20250509162800](https://img.isxcode.com/picgo/20250509162800.png)

#### 17. 创建智能体

![20250509163100](https://img.isxcode.com/picgo/20250509163100.png)

![20250509163131](https://img.isxcode.com/picgo/20250509163131.png)

#### 18. 回到首页对话

![20250522181901](https://img.isxcode.com/picgo/20250522181901.png)

### 更多功能请看产品手册