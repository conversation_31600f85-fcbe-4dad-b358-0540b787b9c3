---
title: "服务器迁移"
---

## 服务器迁移

#### 数据备份

```bash
cd /opt/zhishuyun
# 压缩数据
tar -czvf resources.tar.gz resources
tar -czvf resources_bak.tar.gz resources_bak
# 下载数据
scp root@127.0.0.1:/opt/zhishuyun/resources.tar.gz ~/Downloads/
scp root@127.0.0.1:/opt/zhishuyun/resources_bak.tar.gz ~/Downloads/
```

#### 项目配置文件备份

```bash
scp root@127.0.0.1:/root/application-local.yml ~/Downloads/
```

#### ssl证书备份

```bash
scp root@127.0.0.1:/root/zhishuyun-demo.isxcode.com.key ~/Downloads/
scp root@127.0.0.1:/root/zhishuyun-demo.isxcode.com.pem ~/Downloads/
scp root@127.0.0.1:/root/zhishuyun.isxcode.com.key ~/Downloads/
scp root@127.0.0.1:/root/zhishuyun.isxcode.com.pem ~/Downloads/
```

#### nginx配置文件备份

```bash
scp root@127.0.0.1:/etc/nginx/nginx.conf ~/Downloads/
```

#### 新服务器端口号开放

- 系统：CentOS8.5
- 端口号开放：22、80、443

#### 新服务器-java安装

```bash
scp /Users/<USER>/OneDrive/Downloads/linux/java/zulu8.78.0.19-ca-jdk8.0.412-linux_x64.tar.gz root@127.0.0.1:/tmp
tar -vzxf /tmp/zulu8.78.0.19-ca-jdk8.0.412-linux_x64.tar.gz -C /opt
ln -s /opt/zulu8.78.0.19-ca-jdk8.0.412-linux_x64 /opt/java
tee -a ~/.bashrc <<-'EOF'
export JAVA_HOME=/opt/java
export PATH=$PATH:$JAVA_HOME/bin
EOF
source ~/.bashrc
rm -f /tmp/zulu8.78.0.19-ca-jdk8.0.412-linux_x64.tar.gz
java -version
```

#### 新服务器-nodejs安装

```bash
scp /Users/<USER>/OneDrive/Downloads/linux/node/node-v18.20.5-linux-x64.tar.xz root@127.0.0.1:/tmp
cd /tmp
tar -xvJf node-v18.20.5-linux-x64.tar.xz
mv node-v18.20.5-linux-x64 /opt/
ln -s /opt/node-v18.20.5-linux-x64 /opt/node 
tee -a ~/.bashrc <<-'EOF'
export NODE_HOME=/opt/node 
export PATH=$PATH:${NODE_HOME}/bin
EOF
source ~/.bashrc
npm config set registry https://registry.npmmirror.com/
npm install pm2 -g
rm -f /tmp/node-v18.20.5-linux-x64.tar.xz
pm2 -version
```

#### 新服务器-nginx安装

```bash
scp /Users/<USER>/OneDrive/Downloads/linux/nginx/nginx-1.26.2-1.el8.ngx.x86_64.rpm root@127.0.0.1:/tmp
cd /tmp
rpm -ivh nginx-1.26.2-1.el8.ngx.x86_64.rpm
rm -f /tmp/nginx-1.26.2-1.el8.ngx.x86_64.rpm
nginx
```

#### 上传文件

```bash
scp /Users/<USER>/Downloads/zhishuyun-demo.isxcode.com.pem root@127.0.0.1:/root
scp /Users/<USER>/Downloads/zhishuyun-demo.isxcode.com.key root@127.0.0.1:/root
scp /Users/<USER>/Downloads/zhishuyun.isxcode.com.key root@127.0.0.1:/root
scp /Users/<USER>/Downloads/zhishuyun.isxcode.com.pem root@127.0.0.1:/root
scp /Users/<USER>/Downloads/application-local.yml root@127.0.0.1:/root
scp /Users/<USER>/Downloads/resources.tar.gz root@127.0.0.1:/root
scp /Users/<USER>/Downloads/nginx.conf root@127.0.0.1:/root
```

#### 移动文件

```bash
mkdir -p /data/nginx/ssl/
cp /root/zhishuyun-demo.isxcode.com.pem /data/nginx/ssl/
cp /root/zhishuyun-demo.isxcode.com.key /data/nginx/ssl/
cp /root/zhishuyun.isxcode.com.pem /data/nginx/ssl/
cp /root/zhishuyun.isxcode.com.key /data/nginx/ssl/

mv /etc/nginx/nginx.conf /etc/nginx/nginx.conf_bak
mv /root/nginx.conf /etc/nginx/nginx.conf

mkdir -p /opt/zhishuyun
mkdir -p /opt/zhishuyun/resources_bak
tar -vzxf /root/resources.tar.gz -C /opt/zhishuyun
rm -f /root/resources.tar.gz
```

#### github启动前端脚本

- https://github.com/isxcode/torch-yun/actions/workflows/website-deploy.yml

> 修复一下nginx.pid

```bash
ps -C nginx -o pid= | head -n 1 | awk '{print $1}' > /run/nginx.pid
cat /run/nginx.pid
```

#### github启动后端脚本

- https://github.com/isxcode/torch-yun/actions/workflows/zhishuyun-deploy.yml