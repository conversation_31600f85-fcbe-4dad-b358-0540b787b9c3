---
title: "离线部署"
---

## 使用官网安装包下载部署

#### 下载安装包

官网链接：https://zhishuyun.isxcode.com/

![20240427182151](https://img.isxcode.com/picgo/20240427182151.png)

- [安装包快捷下载链接](https://isxcode.oss-cn-shanghai.aliyuncs.com/zhishuyun/zhishuyun.tar.gz) 
- [许可证快捷下载链接](https://isxcode.oss-cn-shanghai.aliyuncs.com/zhishuyun/license.lic)

#### 下载解压安装包

```bash
cd /tmp
nohup wget https://isxcode.oss-cn-shanghai.aliyuncs.com/zhishuyun/zhishuyun.tar.gz >> download_zhishuyun.log 2>&1 &
tail -f download_zhishuyun.log
tar -vzxf zhishuyun.tar.gz
```

#### 离线安装java环境

```bash
cd /tmp
nohup wget https://openfly.oss-cn-shanghai.aliyuncs.com/java/zulu8.78.0.19-ca-jdk8.0.412-linux_x64.tar.gz >> download_jdk.log 2>&1 &
tail -f download_jdk.log
```

> 注意！！！一定要在~/.bashrc中配置JAVA_HOME环境变量

```bash
tar -vzxf /tmp/zulu8.78.0.19-ca-jdk8.0.412-linux_x64.tar.gz -C /opt
ln -s /opt/zulu8.78.0.19-ca-jdk8.0.412-linux_x64 /opt/java
tee -a ~/.bashrc <<-'EOF'
export JAVA_HOME=/opt/java
export PATH=$PATH:$JAVA_HOME/bin
EOF
source ~/.bashrc
java -version
```

#### 修改管理员登陆密码

```bash
vim zhishuyun/conf/application-local.yml
```

```yml
isx-app:
  admin-passwd: admin1234
```

#### 启动项目

```bash
bash zhishuyun/bin/start.sh
```

#### 访问项目

- 访问项目: http://localhost:8080 
- 管理员账号：`admin` 
- 管理员密码：`admin1234` 