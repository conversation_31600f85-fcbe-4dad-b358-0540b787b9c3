---
title: "开机自启"
---

## 基于CentOS7.9配置至数云开机自启

#### 将脚本文件赋予可执行权限

```bash
chmod a+x /opt/zhishuyun/bin/start.sh
chmod a+x /opt/zhishuyun/bin/stop.sh
```

#### 配置自启文件

```bash
vim /usr/lib/systemd/system/zhishuyun.service
```

> 配置启动脚本路径和pid文件路径和启动用户

```bash
[Unit]
Description=Zhiqingyun Service unit Configuration
After=network.target

[Service]
Type=forking

ExecStart=/opt/zhishuyun/bin/start.sh --print-log="false"
ExecStop=/opt/zhishuyun/bin/stop.sh
ExecReload=/opt/zhishuyun/bin/start.sh --print-log="false"
PIDFile=/opt/zhishuyun/zhishuyun.pid
KillMode=none
Restart=always
User=root
Group=root

[Install]
WantedBy=multi-user.target
```

#### 重载服务

```bash
systemctl daemon-reload
```

#### 设置开机自启

```bash
systemctl enable zhishuyun
```

#### 相关操作命令

```bash
# 启动至数云
systemctl start zhishuyun

# 查看至数云状态
systemctl status zhishuyun

# 停止至数云
systemctl stop zhishuyun

# 重启至数云
systemctl restart zhishuyun
```