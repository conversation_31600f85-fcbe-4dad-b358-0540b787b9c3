---
title: "发版规范"
---

## 发布需求

以发版`GH-1309`需求为例

> 建议使用官方开发工具--**isx-cli**  
> github地址：https://github.com/isxcode/isx-cli
	
#### 1.备份代码

> 将isxcode组织中需求分支代码，强行推送到自己的仓库备份，防止代码回滚。

```bash
# 更新组织代码
isx git fetch upstream
# 切换到需求分支
isx checkout 1309
# 更新需求分支代码
isx pull
# 强行推到自己的仓库
isx push -f
```

#### 2.合并开源代码冲突

```bash
cd torch-yun
git rebase upstream/main
git rebase --continue
```

#### 3.合并闭源代码冲突

```bash
cd torch-yun-vip
git rebase upstream/main
git rebase --continue
```

#### 4.检查sql脚本文件名称规范，检查多数据源脚本是否有遗漏

> V${sql_index}__GH-${issue_number}.sql

```wikitext
resources/db/migration/h2/V22__GH-1309.sql
resources/db/migration/mysql/V22__GH-1309.sql
resources/db/migration/postgres/V22__GH-1309.sql
```

#### 5.修改VERSION中的版本号

```bash
vim resources/VERSION

GH-1309
```

#### 6.本地测试是否可以正常打包

```bash
isx package
```

#### 7.使用本地的docker启动数据源，测试多数据库

##### H2
> org.h2.Driver   
> jdbc:h2:file:~/.zhishuyun/h2/data;AUTO_SERVER=TRUE

##### Mysql
> com.mysql.cj.jdbc.Driver  
> ***************************************

##### Postgresql
> org.postgresql.Driver  
> ********************************************

#### 8.调整sql脚本，并强行推送代码

```bash
isx git push upstream GH-1309 -f
```

#### 9.提交pr到main分支

```bash
isx pr 1309 -m
```

#### 10.Github界面使用`Squash and merge`方式合并

https://github.com/isxcode/torch-yun/pulls  
https://github.com/isxcode/torch-yun-vip/pulls

![20241212160344](https://img.isxcode.com/picgo/20241212160344.png)

#### 11.Github中关闭issue，并删除分支

https://github.com/isxcode/torch-yun/issues/1309

```bash
isx delete 1309
```

#### 12.GitHub action中先打包后部署

https://github.com/isxcode/torch-yun/actions/workflows/build.yml  
https://github.com/isxcode/torch-yun/actions/workflows/zhishuyun-deploy.yml

![20241126225107](https://img.isxcode.com/picgo/20241126225107.png)
