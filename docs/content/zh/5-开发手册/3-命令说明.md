---
title: "命令说明"
---

### 命令说明

#### 清除打包文件

```bash
gradle clean
```

#### 安装依赖

> 只需要安装一次即可

```bash
gradle install
```

#### 代码格式化

```bash
gradle format
```

#### 项目打包

```bash
gradle package
```

#### 本地启动项目

```bash
gradle start
```

#### 打包docker镜像

> 版本指定在 `torch-yun-backend/torch-yun-main/src/main/resources/VERSION` 中

```bash
gradle docker
```

#### 启动官方文档

> 访问地址，在运行日志中

```bash
gradle website
```

#### 单独启动前端服务

```bash
gradle frontend
```

#### 单独启动后端服务

```bash
gradle backend
```

#### 执行自动化测试

```bash
gradle autotest
```

